// Test script to check mock test data in localStorage
// Run this in the browser console to debug data issues

console.log('=== Mock Test Data Debug ===');

// Get all localStorage keys related to mock tests
const allKeys = Object.keys(localStorage);
const mockTestKeys = allKeys.filter(key => key.includes('mock'));

console.log('All localStorage keys:', allKeys);
console.log('Mock test related keys:', mockTestKeys);

// Check for user-specific mock test data
const userKeys = allKeys.filter(key => key.startsWith('mock_tests_'));
console.log('User mock test keys:', userKeys);

userKeys.forEach(key => {
  const data = localStorage.getItem(key);
  console.log(`\n=== ${key} ===`);
  try {
    const parsed = JSON.parse(data);
    console.log('Parsed data:', parsed);
    
    if (Array.isArray(parsed)) {
      parsed.forEach((test, index) => {
        console.log(`\nTest ${index + 1}:`, test.name);
        console.log('- ID:', test.id);
        console.log('- Mistakes:', test.mistakes);
        console.log('- Takeaways:', test.takeaways);
        console.log('- Syllabus:', test.syllabus);
        console.log('- Test Type:', test.testType);
        console.log('- Confidence Level:', test.confidenceLevel);
        console.log('- Notes:', test.notes);
      });
    }
  } catch (e) {
    console.log('Raw data:', data);
  }
});

// Check enhanced data storage
const enhancedKeys = allKeys.filter(key => key.includes('enhanced'));
console.log('\nEnhanced data keys:', enhancedKeys);

enhancedKeys.forEach(key => {
  const data = localStorage.getItem(key);
  console.log(`\n=== ${key} ===`);
  try {
    const parsed = JSON.parse(data);
    console.log('Enhanced data:', parsed);
  } catch (e) {
    console.log('Raw data:', data);
  }
});

// Check mistakes and takeaways storage
const mistakeKeys = allKeys.filter(key => key.includes('mistake'));
const takeawayKeys = allKeys.filter(key => key.includes('takeaway'));

console.log('\nMistake keys:', mistakeKeys);
console.log('Takeaway keys:', takeawayKeys);

mistakeKeys.forEach(key => {
  const data = localStorage.getItem(key);
  console.log(`\n=== ${key} ===`);
  try {
    const parsed = JSON.parse(data);
    console.log('Mistakes data:', parsed);
  } catch (e) {
    console.log('Raw data:', data);
  }
});

takeawayKeys.forEach(key => {
  const data = localStorage.getItem(key);
  console.log(`\n=== ${key} ===`);
  try {
    const parsed = JSON.parse(data);
    console.log('Takeaways data:', parsed);
  } catch (e) {
    console.log('Raw data:', data);
  }
});

console.log('\n=== End Debug ===');
